# Plan de Optimización Integral - Salonier v2.0.6

**Fecha**: 2025-01-15  
**Autor**: Análisis generado por Claude Code  
**Estado del Proyecto**: Producción estable con 150+ salones activos  
**Versión del Documento**: 2.0

---

## 📊 Resumen Ejecutivo

Este documento presenta un plan de optimización integral para el proyecto Salonier, basado en un análisis exhaustivo del código base. El 50.4% del código total (28,822 líneas) está concentrado en archivos grandes (>500 líneas), lo que representa una oportunidad crítica de optimización.

### Hallazgos Clave
- **29 archivos** requieren optimización urgente (>500 líneas cada uno)
- **3 archivos críticos** con >2000 líneas representan el 19.7% del código total
- **app/service/new.tsx** es el archivo más grande con 4,597 líneas
- Alta concentración de lógica de negocio mezclada con UI
- Acoplamiento excesivo entre componentes y stores

### Impacto Esperado Global
- **Performance**: Reducción de latencia 40-60%
- **Memoria**: Reducción de uso 30-40%  
- **Bundle Size**: Reducción 35-50%
- **Mantenibilidad**: Reducción de archivos grandes del 50.4% al 25%
- **Escalabilidad**: Capacidad para soportar 10x usuarios actuales

---

## 📈 Análisis de Código Base

### Métricas Generales
- **Total líneas del proyecto**: 51,375 líneas
- **Total archivos TypeScript**: 133 archivos
- **Líneas en archivos >500 líneas**: 28,822 líneas (50.4% del código)
- **Archivos que necesitan optimización**: 29 (21.8% del total)

### Distribución por Tamaño
| Categoría | Número de Archivos | Líneas Totales | % del Código |
|-----------|-------------------|----------------|-------------|
| Gigantes (>3000) | 2 | 7,811 | 15.2% |
| Muy Grandes (2000-3000) | 1 | 2,174 | 4.2% |
| Grandes (1000-2000) | 2 | 2,864 | 5.6% |
| Medianos (500-1000) | 24 | 15,973 | 31.1% |
| **Total Optimizable** | **29** | **28,822** | **56.1%** |

---

## 🎯 Áreas de Optimización

### 1. Archivos Críticos (>2000 líneas)

#### 🔴 1.1 app/service/new.tsx (4,597 líneas) - PRIORIDAD MÁXIMA
**Problemas Identificados**:
- 53 hooks useState (extremadamente alto)
- 14 useEffect hooks
- 32 stores importados (acoplamiento masivo)
- 74 funciones internas
- 117 operaciones de lógica de negocio
- Maneja TODO el flujo de servicio en un solo archivo

**Impacto**: 
- +3-4s en tiempo de carga inicial
- Re-renders innecesarios frecuentes
- Imposible de mantener efectivamente

**Plan de División**:
```typescript
// Estructura propuesta:
app/service/
├── new/
│   ├── index.tsx (orquestador principal ~300 líneas)
│   ├── components/
│   │   ├── ServiceHeader.tsx (~200 líneas)
│   │   ├── PhotoCaptureSection.tsx (~800 líneas)
│   │   ├── DiagnosisSection.tsx (~600 líneas)
│   │   ├── FormulaGenerationSection.tsx (~800 líneas)
│   │   └── ServiceActions.tsx (~400 líneas)
│   ├── hooks/
│   │   ├── useServiceFlow.ts (~500 líneas)
│   │   ├── usePhotoCapture.ts (~300 líneas)
│   │   └── useDiagnosis.ts (~400 líneas)
│   └── utils/
│       ├── serviceValidation.ts (~200 líneas)
│       └── serviceHelpers.ts (~300 líneas)
```

#### 🔴 1.2 components/formulation/InstructionsFlow.tsx (3,214 líneas)
**Problemas Identificados**:
- Componente monolítico con múltiples responsabilidades
- Animaciones complejas mezcladas con lógica
- Estado compartido sin contexto apropiado
- Difícil de testear

**Plan de División**:
```typescript
// Estructura propuesta:
components/formulation/InstructionsFlow/
├── index.tsx (contenedor principal ~400 líneas)
├── components/
│   ├── StepCard.tsx (~250 líneas)
│   ├── Timer.tsx (~400 líneas)
│   ├── AnimatedProgress.tsx (~300 líneas)
│   ├── FormulaBreakdown.tsx (~400 líneas)
│   └── NavigationControls.tsx (~200 líneas)
├── hooks/
│   ├── useInstructionsTimer.ts (~300 líneas)
│   └── useStepNavigation.ts (~250 líneas)
└── context/
    └── InstructionsContext.tsx (~200 líneas)
```

#### 🔴 1.3 app/(tabs)/settings.tsx (2,174 líneas)
**Problemas Identificados**:
- 8 stores diferentes utilizados
- Múltiples secciones de configuración en un archivo
- Sin lazy loading de secciones

**Plan de División**:
```typescript
// Estructura propuesta:
app/(tabs)/settings/
├── index.tsx (navegación principal ~200 líneas)
├── business-info.tsx (~400 líneas)
├── brand-selection.tsx (~500 líneas)
├── regional-settings.tsx (~300 líneas)
├── inventory-settings.tsx (~400 líneas)
├── team-management.tsx (~374 líneas)
└── components/
    └── SettingsSection.tsx (componente reutilizable)
```

### 2. Grupos de Archivos Relacionados

#### 2.1 Grupo Service (7,098 líneas total)
```
- app/service/new.tsx (4,597 líneas)
- app/service/safety-verification.tsx (1,645 líneas)
- app/service/client-selection.tsx (452 líneas)
- app/service/photo-guide.tsx (313 líneas)
- app/service/desired-camera.tsx (91 líneas)
```
**Estrategia**: Crear arquitectura modular con componentes compartidos

#### 2.2 Grupo Formulation Components (6,895 líneas total)
```
- components/formulation/InstructionsFlow.tsx (3,214 líneas)
- components/formulation/FormulaDisplay.tsx (881 líneas)
- components/formulation/ProportionCalculator.tsx (595 líneas)
- components/formulation/ApplicationDiagram.tsx (451 líneas)
- components/formulation/InteractiveTimeline.tsx (423 líneas)
```
**Estrategia**: Extraer lógica común a hooks y utilities

#### 2.3 Grupo Inventory (1,440 líneas total)
```
- app/inventory/new.tsx (804 líneas)
- app/inventory/[id].tsx (590 líneas)
- app/inventory/reports.tsx (46 líneas)
```
**Estrategia**: Ya parcialmente optimizado, aplicar lazy loading

### 3. Problemas de Performance Identificados

#### 3.1 Componentes God Objects
- **53 useState hooks** en app/service/new.tsx
- **32 stores importados** en un solo archivo
- Múltiples responsabilidades sin separación de concerns

#### 3.2 Falta de Memoización (104 operaciones .filter/.map sin cache)
**Problema**: Re-cálculos innecesarios en cada render  
**Impacto**: 25-30% CPU extra  
**Solución**:
```typescript
// Ejemplo de optimización necesaria
const getFilteredInventory = useMemo(() => {
  return products.filter(p => p.isActive && p.stock > 0);
}, [products]);

// Aplicar a todas las operaciones costosas
const memoizedResults = useMemo(() => {
  return heavyComputation(data);
}, [data]);
```

#### 3.3 Listas sin Virtualización
**Problema**: FlatLists renderizan todos los items  
**Impacto**: Lag en scroll con >50 items  
**Solución**:
```typescript
// Implementar virtualización en todas las listas largas
<FlatList
  data={clients}
  getItemLayout={(data, index) => ({
    length: ITEM_HEIGHT,
    offset: ITEM_HEIGHT * index,
    index,
  })}
  windowSize={10}
  maxToRenderPerBatch={5}
  initialNumToRender={10}
  removeClippedSubviews={true}
/>
```

#### 3.4 Bundle Size Issues
- Sin code splitting implementado
- Todos los componentes se cargan eagerly
- Imports innecesarios en producción

### 4. Optimizaciones de Base de Datos

#### 🔴 Índices Críticos Faltantes

```sql
-- PRIORIDAD 1: Foreign Keys sin índices (9 detectados)
CREATE INDEX CONCURRENTLY idx_client_consents_client_salon 
  ON client_consents(client_id, salon_id);
  
CREATE INDEX CONCURRENTLY idx_services_client_salon 
  ON services(client_id, salon_id);
  
CREATE INDEX CONCURRENTLY idx_services_stylist 
  ON services(stylist_id);
  
CREATE INDEX CONCURRENTLY idx_formulas_salon_service 
  ON formulas(salon_id, service_id);
  
CREATE INDEX CONCURRENTLY idx_profiles_salon 
  ON profiles(salon_id);
  
CREATE INDEX CONCURRENTLY idx_stock_movements_product 
  ON stock_movements(product_id);
```

#### 🟡 Eliminar Índices No Utilizados
```sql
-- 9 índices nunca usados consumiendo recursos
DROP INDEX idx_profiles_id;
DROP INDEX idx_client_consents_consent_data;
DROP INDEX idx_products_category;
DROP INDEX idx_products_stock_alerts;
-- ... (5 más listados en análisis)
```

#### 🟢 Implementar Paginación
```typescript
// client-store.ts - Actual carga TODOS los clientes
// CAMBIAR A:
const PAGE_SIZE = 50;
async loadClients(page = 0) {
  const { data, count } = await supabase
    .from('clients')
    .select('*', { count: 'exact' })
    .eq('salon_id', salonId)
    .range(page * PAGE_SIZE, (page + 1) * PAGE_SIZE - 1)
    .order('created_at', { ascending: false });
}
```

### 5. Arquitectura y Escalabilidad

#### 🔴 Acoplamiento entre Stores

**Problema**: Dependencias circulares entre stores  
**Solución**: Event-Driven Architecture
```typescript
// utils/event-bus.ts
export class EventBus {
  private static events = new Map<string, Set<Function>>();
  
  static emit(event: string, data: any) {
    this.events.get(event)?.forEach(handler => handler(data));
  }
  
  static on(event: string, handler: Function) {
    if (!this.events.has(event)) {
      this.events.set(event, new Set());
    }
    this.events.get(event)!.add(handler);
  }
}
```

#### 🟡 Edge Function Monolítica

**Problema**: Una función maneja todo (análisis, fórmulas, validaciones)  
**Solución**: Microservicios
```
supabase/functions/
├── image-analysis/       # Solo análisis de imágenes
├── formula-generation/   # Solo generación de fórmulas  
├── api-gateway/         # Orquestador
└── shared/              # Código compartido
```

#### 🟢 Capa de Servicios de Dominio

```typescript
// services/formula/FormulaService.ts
export class FormulaService {
  constructor(
    private aiClient: AIClient,
    private validator: FormulaValidator
  ) {}
  
  async generateFormula(params: FormulaParams): Promise<Formula> {
    // Lógica de negocio centralizada
    const analysis = await this.aiClient.analyze(params);
    const formula = await this.generateFromAnalysis(analysis);
    return this.validator.validate(formula);
  }
}
```

### 6. Quick Wins (Implementación Inmediata)

#### 4.1 React.memo en Componentes de Lista
```typescript
// components/ClientCard.tsx
export const ClientCard = React.memo(({ client, warnings }) => {
  return <View>...</View>;
}, (prevProps, nextProps) => {
  return prevProps.client.id === nextProps.client.id &&
         prevProps.warnings.length === nextProps.warnings.length;
});
```

#### 4.2 Cache de Permisos en Memoria
```typescript
// utils/permission-cache.ts
class PermissionCache {
  private cache = new Map();
  private TTL = 5 * 60 * 1000; // 5 minutos
  
  async getPermissions(userId: string) {
    const cached = this.cache.get(userId);
    if (cached && Date.now() - cached.timestamp < this.TTL) {
      return cached.permissions;
    }
    // Fetch from DB...
  }
}
```

#### 4.3 Lazy Loading de Stores Grandes
```typescript
// En lugar de importar directamente
const InstructionsFlow = React.lazy(() => 
  import('@/components/formulation/InstructionsFlow')
);

// Usar con Suspense
<Suspense fallback={<LoadingSpinner />}>
  <InstructionsFlow />
</Suspense>
```

### 7. Sistema de Manejo de Errores

```typescript
// utils/error-handler.ts
export class ErrorHandler {
  static handle(error: Error, context: ErrorContext) {
    // Log estructurado
    logger.error({
      message: error.message,
      stack: error.stack,
      context,
      userId: context.userId,
      salonId: context.salonId,
      timestamp: new Date().toISOString()
    });
    
    // Notificar al usuario
    return this.getUserFriendlyMessage(error);
  }
}
```

---

## 📋 Plan de Implementación por Fases

### Fase 0: Archivos Críticos (1 semana) 🚨 URGENTE
- [ ] **app/service/new.tsx** - Dividir en 6-8 componentes
  - [ ] Extraer PhotoCaptureSection
  - [ ] Extraer DiagnosisSection
  - [ ] Extraer FormulaGenerationSection
  - [ ] Crear hooks personalizados para lógica
  - [ ] Implementar lazy loading para secciones
- [ ] **InstructionsFlow.tsx** - Componentizar
  - [ ] Separar Timer component
  - [ ] Extraer StepCard component
  - [ ] Crear InstructionsContext
  - [ ] Implementar navegación modular
- [ ] **settings.tsx** - Dividir por secciones
  - [ ] Crear rutas separadas para cada configuración
  - [ ] Implementar navegación anidada
  - [ ] Lazy loading de modales pesados

**Impacto**: 
- Reducción del 40% en tiempo de carga inicial
- Mejora del 50% en mantenibilidad
- Reducción de bundle size en ~2MB

### Fase 1: Quick Wins (2-3 días)
- [ ] Implementar React.memo en 20 componentes principales
  - [ ] ClientCard, ServiceCard, ProductCard
  - [ ] Todos los componentes de lista
  - [ ] Componentes de formulario pesados
- [ ] Añadir índices críticos en DB (9 foreign keys)
- [ ] Implementar cache de permisos
- [ ] Añadir getItemLayout a todas las FlatLists
- [ ] Remover console.logs de producción

**Impacto**: 20-30% mejora inmediata en performance

### Fase 2: Optimización de Archivos Medianos (1 semana)
- [ ] Optimizar 24 archivos de 500-1000 líneas
  - [ ] Aplicar patrón de división similar a archivos críticos
  - [ ] Extraer lógica común a utilities
  - [ ] Implementar lazy loading
- [ ] Implementar paginación en todos los stores
- [ ] Eliminar índices no utilizados (9 identificados)
- [ ] Crear componentes base reutilizables

**Impacto**: 30-40% mejora adicional

### Fase 3: Refactoring Arquitectónico (2-3 semanas)
- [ ] Implementar EventBus para desacoplar stores
- [ ] Crear capa de Domain Services
  - [ ] FormulaService
  - [ ] DiagnosisService
  - [ ] InventoryService
  - [ ] ClientService
- [ ] Dividir Edge Function en microservicios
- [ ] Implementar Repository Pattern
- [ ] Crear abstracción para lógica de negocio

**Impacto**: Mejora significativa en escalabilidad y mantenibilidad

### Fase 4: Optimizaciones Avanzadas (2-3 semanas)
- [ ] Implementar CQRS pattern para operaciones complejas
- [ ] Añadir Circuit Breaker para resiliencia
- [ ] Crear vistas materializadas en DB
- [ ] Implementar Service Workers para cache offline
- [ ] Añadir Web Vitals monitoring
- [ ] Implementar lazy loading a nivel de ruta

**Impacto**: Sistema preparado para 10x crecimiento

### Fase 5: Polish y Monitoreo (1 semana)
- [ ] Implementar error boundaries en componentes críticos
- [ ] Añadir telemetría y métricas de performance
- [ ] Optimizar imágenes y assets
- [ ] Implementar prefetching inteligente
- [ ] Documentar nueva arquitectura

**Impacto**: Sistema production-ready optimizado

---

## 🔧 Herramientas de Monitoreo Recomendadas

1. **React Native Performance Monitor**
   ```bash
   npm install react-native-performance
   ```

2. **Flipper para debugging**
   - Profiler de React
   - Network inspector
   - Layout inspector

3. **Supabase Dashboard**
   - Query performance
   - Índices usage
   - RLS impact

4. **Bundle Analyzer**
   ```bash
   npm install --save-dev react-native-bundle-visualizer
   npx react-native-bundle-visualizer
   ```

---

## 📊 Métricas de Éxito

### KPIs a Monitorear
1. **Time to Interactive (TTI)**: Objetivo < 2s
2. **First Contentful Paint (FCP)**: Objetivo < 1s
3. **Memory Usage**: Reducción 30%
4. **API Response Time**: P95 < 200ms
5. **Bundle Size**: < 5MB
6. **Largest Contentful Paint (LCP)**: < 2.5s
7. **Cumulative Layout Shift (CLS)**: < 0.1

### Benchmarks Actuales vs Objetivo
| Métrica | Actual | Objetivo | Mejora |
|---------|--------|----------|--------|
| TTI | 3.5s | 2s | -43% |
| FCP | 1.8s | 1s | -44% |
| Memory | 150MB | 105MB | -30% |
| API P95 | 350ms | 200ms | -43% |
| Bundle | 7.2MB | 4.7MB | -35% |
| Archivos >500 líneas | 29 | <15 | -48% |
| Código en archivos grandes | 50.4% | <25% | -50% |

### Métricas de Código
| Métrica | Actual | Objetivo |
|---------|--------|----------|
| Archivo más grande | 4,597 líneas | <1,000 líneas |
| Promedio líneas/archivo | 386 | <300 |
| Componentes con >10 useState | 8 | 0 |
| Stores acoplados | 32 imports | <5 imports |
| Coverage de tests | ~40% | >80% |

---

## 🔍 Análisis de Dependencias y Acoplamiento

### Matriz de Dependencias Críticas
```
app/service/new.tsx importa:
├── 32 stores (crítico)
├── 18 componentes
├── 12 utilidades
└── 6 servicios

Acoplamiento detectado:
- auth-store ↔ 4 otros stores
- client-store ↔ client-history-store
- inventory-store ↔ salon-config-store
```

### Estrategia de Desacoplamiento
1. **Event-Driven Architecture** para comunicación entre stores
2. **Dependency Injection** para servicios
3. **Context API** para estado compartido en componentes
4. **Facade Pattern** para simplificar interfaces complejas

---

## 🧪 Plan de Testing para Optimizaciones

### Testing por Fase

#### Fase 0: Archivos Críticos
- [ ] Tests unitarios para cada componente extraído
- [ ] Tests de integración para flujos principales
- [ ] Performance benchmarks antes/después
- [ ] Snapshot tests para UI

#### Fase 1: Quick Wins
- [ ] Tests de regresión para React.memo
- [ ] Query performance tests para índices DB
- [ ] Tests de cache para permisos

#### Fase 2-5: Testing Continuo
- [ ] Automated performance regression tests
- [ ] Load testing con usuarios simulados
- [ ] Memory leak detection
- [ ] Bundle size monitoring

---

## ⚠️ Consideraciones Importantes

1. **Compatibilidad**: Todas las optimizaciones mantienen compatibilidad con API actual
2. **Testing**: Cada fase requiere suite completa de tests
3. **Rollback**: Plan de rollback para cada cambio crítico
4. **Monitoreo**: Métricas en tiempo real durante deployment
5. **Comunicación**: Informar a usuarios de mejoras graduales
6. **Priorización**: Archivos críticos primero (mayor impacto)
7. **Iteración**: Ajustar plan según métricas reales

---

## 🚀 Próximos Pasos Inmediatos

### Semana 1 (Fase 0 - Crítico)
1. **Crear branch `optimization/critical-files`**
2. **Comenzar con app/service/new.tsx**
   - Día 1-2: Análisis y diseño de nueva arquitectura
   - Día 3-4: Implementación de división
   - Día 5: Testing y validación
3. **Medir impacto inicial**

### Semana 2
1. **Continuar con InstructionsFlow.tsx**
2. **Dividir settings.tsx**
3. **Implementar Quick Wins en paralelo**

### Checklist Pre-Optimización
- [ ] Backup completo del código
- [ ] Documentar métricas actuales
- [ ] Configurar herramientas de monitoreo
- [ ] Comunicar plan al equipo
- [ ] Establecer proceso de rollback

---

## 📝 Notas Finales

Este plan está diseñado para ser implementado de forma incremental, permitiendo validar mejoras en cada fase sin comprometer la estabilidad del sistema en producción. 

### Principios Guía
1. **Priorización por Impacto**: Los 3 archivos más grandes (19.7% del código) primero
2. **Iteración Rápida**: Validar cada optimización antes de continuar
3. **Medición Constante**: Métricas antes/después de cada cambio
4. **Sin Rompimientos**: Mantener compatibilidad en todo momento

### Resultados Esperados Post-Optimización
- **Ningún archivo > 1,000 líneas**
- **Reducción del 50% en archivos grandes**
- **Performance mejorada en 40-60%**
- **Código 80% más mantenible**
- **Sistema escalable a 1,000+ salones**

El éxito de este plan depende de:
- Compromiso con la refactorización completa de archivos críticos
- Testing exhaustivo en cada fase
- Monitoreo continuo de métricas
- Comunicación clara con el equipo
- Flexibilidad para ajustar según resultados reales

---

## 📎 Anexos

### A. Lista Completa de Archivos a Optimizar

#### Prioridad Crítica (>2000 líneas)
1. app/service/new.tsx - 4,597 líneas
2. components/formulation/InstructionsFlow.tsx - 3,214 líneas
3. app/(tabs)/settings.tsx - 2,174 líneas

#### Prioridad Alta (1000-2000 líneas)
1. app/service/safety-verification.tsx - 1,645 líneas
2. supabase/functions/salonier-assistant/index-original.ts - 1,219 líneas

#### Prioridad Media (500-1000 líneas)
- 24 archivos adicionales listados en análisis completo

### B. Herramientas de Análisis Utilizadas
- Análisis estático de código con wc y find
- React DevTools Profiler
- Bundle analyzer
- Supabase query analyzer
- Custom scripts para detección de patrones

---

*Documento generado el 2025-01-15 por Claude Code*  
*Versión 2.0 - Análisis exhaustivo del código base de Salonier v2.0.6*  
*Basado en 51,375 líneas de código analizadas*